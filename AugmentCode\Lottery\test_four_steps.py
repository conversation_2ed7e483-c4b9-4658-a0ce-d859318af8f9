#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试四步彩票分析程序
"""

import pandas as pd
import numpy as np
from lottery_analysis_four_steps import LotteryAnalysisFourSteps

def create_test_data():
    """
    创建测试数据
    """
    print("创建测试数据...")
    
    # 创建SSQ测试数据
    ssq_data = []
    for i in range(1, 3001):  # 创建3000期数据
        period = 2024000 + i
        # 随机生成红球号码（1-33，6个不重复）
        red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
        # 随机生成蓝球号码（1-16，1个）
        blue_ball = np.random.choice(range(1, 17), 1)[0]
        
        # 创建完整行数据（模拟Excel的A列和I-O列）
        row = [period] + [0] * 7 + red_balls + [blue_ball]
        ssq_data.append(row)
    
    # 创建DLT测试数据
    dlt_data = []
    for i in range(1, 3001):  # 创建3000期数据
        period = 24000 + i
        # 随机生成红球号码（1-35，5个不重复）
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        # 随机生成蓝球号码（1-12，2个不重复）
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        
        # 创建完整行数据（模拟Excel的A列和H-N列）
        row = [period] + [0] * 6 + red_balls + blue_balls
        dlt_data.append(row)
    
    # 创建DataFrame
    ssq_columns = ['期号'] + [f'Col{i}' for i in range(1, 8)] + ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']
    dlt_columns = ['期号'] + [f'Col{i}' for i in range(1, 7)] + ['红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
    
    ssq_df = pd.DataFrame(ssq_data, columns=ssq_columns)
    dlt_df = pd.DataFrame(dlt_data, columns=dlt_columns)
    
    # 保存到Excel文件
    with pd.ExcelWriter('lottery_data_all.xlsx', engine='openpyxl') as writer:
        ssq_df.to_excel(writer, sheet_name='SSQ_data_all', index=False)
        dlt_df.to_excel(writer, sheet_name='DLT_data_all', index=False)
    
    print("测试数据创建完成！")
    return True

def test_step1():
    """
    测试第一步：读取数据
    """
    print("\n测试第一步：读取数据")
    print("-" * 40)
    
    analyzer = LotteryAnalysisFourSteps()
    result = analyzer.step1_read_and_sort_data()
    
    if result:
        print("✓ 第一步测试通过")
        return analyzer
    else:
        print("✗ 第一步测试失败")
        return None

def test_step2(analyzer):
    """
    测试第二步：指定参数（自动化测试）
    """
    print("\n测试第二步：指定参数")
    print("-" * 40)
    
    # 模拟用户输入
    analyzer.lottery_type = "SSQ"
    analyzer.selected_data = analyzer.ssqhistory_allout
    analyzer.start_row = 2000
    analyzer.calculation_method = "集成预测"
    
    print(f"彩票类型: {analyzer.lottery_type}")
    print(f"开始行: {analyzer.start_row}")
    print(f"计算方法: {analyzer.calculation_method}")
    print("✓ 第二步测试通过")
    
    return True

def test_prediction_methods(analyzer):
    """
    测试预测方法
    """
    print("\n测试预测方法")
    print("-" * 40)
    
    # 获取测试数据
    test_data = analyzer.selected_data.iloc[:100]  # 使用前100期数据
    
    # 测试贝叶斯预测
    print("测试贝叶斯预测...")
    bayesian_result = analyzer.bayesian_prediction(test_data)
    print(f"贝叶斯预测结果: 红球{bayesian_result['red_numbers']}, 蓝球{bayesian_result['blue_numbers']}")
    
    # 测试马尔可夫预测
    print("测试马尔可夫预测...")
    markov_result = analyzer.markov_prediction(test_data)
    print(f"马尔可夫预测结果: 红球{markov_result['red_numbers']}, 蓝球{markov_result['blue_numbers']}")
    
    # 测试集成预测
    print("测试集成预测...")
    ensemble_result = analyzer.ensemble_prediction(test_data)
    print(f"集成预测结果: 红球{ensemble_result['red_numbers']}, 蓝球{ensemble_result['blue_numbers']}")
    
    print("✓ 预测方法测试通过")
    return True

def test_step3_mini(analyzer):
    """
    测试第三步：小规模计算验证
    """
    print("\n测试第三步：小规模计算验证")
    print("-" * 40)
    
    # 设置小规模测试参数
    original_start_row = analyzer.start_row
    analyzer.start_row = 100  # 从第100行开始
    
    # 只测试10次预测
    max_test_iterations = 10
    test_results = []
    
    for i in range(max_test_iterations):
        current_row = analyzer.start_row + i
        
        if current_row + 6 >= len(analyzer.selected_data):
            break
        
        # 获取训练数据
        training_data = analyzer.selected_data.iloc[:current_row].copy()
        
        # 预测
        prediction = analyzer.predict_numbers(training_data)
        
        # 验证（只检查下一期）
        actual_data = analyzer.selected_data.iloc[current_row]
        match_info = analyzer.check_prediction_accuracy(prediction, actual_data)
        
        test_results.append({
            'iteration': i + 1,
            'prediction_period': int(training_data.iloc[-1, 0]),
            'actual_period': int(actual_data.iloc[0]),
            'is_hit': match_info['is_hit'],
            'total_matches': match_info['total_matches']
        })
        
        print(f"第{i+1}次: 基于第{test_results[-1]['prediction_period']}期预测第{test_results[-1]['actual_period']}期, "
              f"匹配{test_results[-1]['total_matches']}个号码, "
              f"{'命中' if test_results[-1]['is_hit'] else '未命中'}")
    
    # 恢复原始参数
    analyzer.start_row = original_start_row
    
    hit_count = sum(1 for r in test_results if r['is_hit'])
    print(f"\n小规模测试完成: {len(test_results)}次预测, {hit_count}次命中")
    print("✓ 第三步测试通过")
    
    return True

def main():
    """
    主测试函数
    """
    print("开始四步彩票分析程序测试")
    print("=" * 60)
    
    # 创建测试数据
    if not create_test_data():
        print("创建测试数据失败")
        return
    
    # 测试第一步
    analyzer = test_step1()
    if not analyzer:
        return
    
    # 测试第二步
    if not test_step2(analyzer):
        return
    
    # 测试预测方法
    if not test_prediction_methods(analyzer):
        return
    
    # 测试第三步（小规模）
    if not test_step3_mini(analyzer):
        return
    
    print("\n" + "=" * 60)
    print("所有测试通过！程序功能正常。")
    print("=" * 60)
    
    # 询问是否运行完整程序
    print("\n是否要运行完整的四步分析程序？(y/n)")
    choice = input().strip().lower()
    
    if choice == 'y':
        print("\n开始运行完整程序...")
        analyzer.run_four_steps_analysis()

if __name__ == "__main__":
    main()
