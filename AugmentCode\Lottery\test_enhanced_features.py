#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试增强功能的脚本
验证新增的分析功能是否正常工作
"""

from lottery_analysis_three_steps import LotteryPredictor

def test_enhanced_features():
    """
    测试增强功能
    """
    print("开始测试增强功能...")
    
    # 创建预测器实例
    predictor = LotteryPredictor()
    
    # 测试数据读取
    print("\n1. 测试数据读取功能...")
    if predictor.step1_read_and_sort_data():
        print("✓ 数据读取功能正常")
    else:
        print("✗ 数据读取功能异常")
        return False
    
    # 设置为双色球进行测试
    predictor.lottery_type = "SSQ"
    predictor.selected_data = predictor.ssqhistory_allout
    
    # 测试质数判断功能
    print("\n2. 测试质数判断功能...")
    test_numbers = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
    expected_primes = [2, 3, 5, 7, 11]
    actual_primes = [n for n in test_numbers if predictor.is_prime(n)]
    
    if actual_primes == expected_primes:
        print("✓ 质数判断功能正常")
    else:
        print(f"✗ 质数判断功能异常: 期望 {expected_primes}, 实际 {actual_primes}")
        return False
    
    # 测试重号分析功能
    print("\n3. 测试重号分析功能...")
    try:
        repeat_stats = predictor.calculate_repeat_analysis()
        if 'red_repeats' in repeat_stats and 'blue_repeats' in repeat_stats:
            print("✓ 重号分析功能正常")
            print(f"  红球重号总次数: {repeat_stats['red_repeat_count']}")
            print(f"  蓝球重号总次数: {repeat_stats['blue_repeat_count']}")
        else:
            print("✗ 重号分析功能异常")
            return False
    except Exception as e:
        print(f"✗ 重号分析功能异常: {e}")
        return False
    
    # 测试模式分析功能（包括质合比例）
    print("\n4. 测试模式分析功能...")
    try:
        patterns = predictor.calculate_pattern_analysis()
        required_keys = ['odd_even', 'big_small', 'prime_composite', 'consecutive']
        
        if all(key in patterns for key in required_keys):
            print("✓ 模式分析功能正常")
            print(f"  奇偶比例模式数: {len(patterns['odd_even'])}")
            print(f"  大小比例模式数: {len(patterns['big_small'])}")
            print(f"  质合比例模式数: {len(patterns['prime_composite'])}")
            print(f"  连号模式数: {len(patterns['consecutive'])}")
        else:
            print("✗ 模式分析功能异常")
            return False
    except Exception as e:
        print(f"✗ 模式分析功能异常: {e}")
        return False
    
    # 测试趋势分析功能（100期）
    print("\n5. 测试趋势分析功能...")
    try:
        trends = predictor.calculate_trend_analysis(100)
        required_keys = ['red_recent', 'blue_recent', 'red_hot', 'red_cold', 'blue_hot', 'blue_cold']
        
        if all(key in trends for key in required_keys):
            print("✓ 趋势分析功能正常")
            print(f"  红球热号数量: {len(trends['red_hot'])}")
            print(f"  红球冷号数量: {len(trends['red_cold'])}")
            print(f"  蓝球热号数量: {len(trends['blue_hot'])}")
            print(f"  蓝球冷号数量: {len(trends['blue_cold'])}")
        else:
            print("✗ 趋势分析功能异常")
            return False
    except Exception as e:
        print(f"✗ 趋势分析功能异常: {e}")
        return False
    
    # 测试预测功能
    print("\n6. 测试预测功能...")
    try:
        # 获取基础数据
        red_freq, blue_freq = predictor.calculate_frequency_statistics()
        
        # 测试贝叶斯预测
        bayesian_pred = predictor.bayesian_prediction(red_freq, blue_freq, trends, patterns)
        if 'red_numbers' in bayesian_pred and 'blue_numbers' in bayesian_pred:
            print("✓ 贝叶斯预测功能正常")
        else:
            print("✗ 贝叶斯预测功能异常")
            return False
        
        # 测试马尔可夫链预测
        markov_pred = predictor.markov_chain_prediction(red_freq, blue_freq, trends, repeat_stats)
        if 'red_numbers' in markov_pred and 'blue_numbers' in markov_pred:
            print("✓ 马尔可夫链预测功能正常")
        else:
            print("✗ 马尔可夫链预测功能异常")
            return False
        
        # 测试集成预测
        ensemble_pred = predictor.ensemble_prediction(bayesian_pred, markov_pred, red_freq, blue_freq, trends)
        if 'red_numbers' in ensemble_pred and 'blue_numbers' in ensemble_pred:
            print("✓ 集成预测功能正常")
        else:
            print("✗ 集成预测功能异常")
            return False
            
    except Exception as e:
        print(f"✗ 预测功能异常: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("所有增强功能测试通过！")
    print("=" * 50)
    
    # 显示预测结果示例
    print(f"\n预测结果示例（双色球）:")
    print(f"贝叶斯预测: 红球{bayesian_pred['red_numbers']} 蓝球{bayesian_pred['blue_numbers']}")
    print(f"马尔可夫预测: 红球{markov_pred['red_numbers']} 蓝球{markov_pred['blue_numbers']}")
    print(f"集成预测: 红球{ensemble_pred['red_numbers']} 蓝球{ensemble_pred['blue_numbers']}")
    
    return True

if __name__ == "__main__":
    test_enhanced_features()
