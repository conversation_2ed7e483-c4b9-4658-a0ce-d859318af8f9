#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速测试彩票分析程序
"""

from lottery_analysis_four_steps import LotteryAnalyzer

def quick_test():
    """快速测试分析功能"""
    analyzer = LotteryAnalyzer()
    
    # 第一步：读取数据
    if not analyzer.step1_read_and_sort_data():
        return
    
    # 设置测试参数
    analyzer.lottery_type = 'SSQ'
    analyzer.data = analyzer.ssqhistory_allout
    analyzer.start_row = 1000
    analyzer.method = 'multi_bayesian'
    
    print('开始测试从第1000行开始的10次计算...')
    analyzer.results = []
    
    for i in range(10):
        current_row = analyzer.start_row + i
        train_data = analyzer.data.iloc[:current_row].copy()
        actual_numbers = analyzer.data.iloc[current_row, 1:].values.tolist()
        predicted_numbers = analyzer.predict_numbers(train_data, analyzer.method)
        
        if predicted_numbers:
            hit_info = analyzer.check_hit_rate(predicted_numbers, actual_numbers)
            print(f'第{i+1}次:')
            print(f'  预测: {predicted_numbers}')
            print(f'  实际: {actual_numbers}')
            print(f'  红球命中: {hit_info["red_hits"]}, 蓝球命中: {hit_info["blue_hits"]}')
            print(f'  是否命中: {"是" if hit_info["is_hit"] else "否"}')
            print()

if __name__ == "__main__":
    quick_test()
